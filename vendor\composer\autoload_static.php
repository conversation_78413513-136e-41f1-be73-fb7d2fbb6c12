<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit323d0ac17d3a649cbcf7feda8d981a84
{
    public static $prefixLengthsPsr4 = array (
        'A' => 
        array (
            'App\\Core\\' => 9,
            'App\\Controllers\\' => 16,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'App\\Core\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app/Core',
        ),
        'App\\Controllers\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app/Controllers',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit323d0ac17d3a649cbcf7feda8d981a84::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit323d0ac17d3a649cbcf7feda8d981a84::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit323d0ac17d3a649cbcf7feda8d981a84::$classMap;

        }, null, ClassLoader::class);
    }
}
